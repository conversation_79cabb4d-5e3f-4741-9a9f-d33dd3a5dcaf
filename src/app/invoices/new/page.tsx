"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon, Plus, Trash2, ArrowLeft, ChevronDown, ChevronUp } from "lucide-react";
import { format, addDays, addMonths } from "date-fns";
import { cn } from "@/lib/utils";
import { calculateInvoiceTotals, formatCurrency } from "@/lib/invoice-utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

const invoiceItemSchema = z.object({
  productId: z.string().min(1, "Product is required"),
  description: z.string().min(1, "Description is required"),
  quantity: z.coerce.number().positive("Quantity must be positive"),
  unitPrice: z.coerce.number().positive("Unit price must be positive"),
});

const installmentSchema = z.object({
  dueDate: z.date(),
  amount: z.coerce.number().positive("Amount must be positive"),
  description: z.string().optional(),
});

const invoiceFormSchema = z.object({
  purchaseOrderId: z.string().optional(),
  invoiceNumber: z.string().min(1, "Invoice number is required"),
  supplierId: z.string().min(1, "Supplier is required"),
  invoiceDate: z.date(),
  dueDate: z.date().optional(),
  taxPercentage: z.coerce.number().min(0).max(100).optional(),
  notes: z.string().optional(),
  items: z.array(invoiceItemSchema).min(1, "At least one item is required"),
  // Installment fields
  enableInstallments: z.boolean(),
  numberOfInstallments: z.coerce.number().min(2).max(12).optional(),
  installments: z.array(installmentSchema).optional(),
}).refine((data) => {
  if (data.enableInstallments) {
    if (!data.numberOfInstallments || data.numberOfInstallments < 2) {
      return false;
    }
    if (!data.installments || data.installments.length !== data.numberOfInstallments) {
      return false;
    }
    // Validate installment amounts sum to total (will be checked in component)
    return true;
  }
  return true;
}, {
  message: "Invalid installment configuration",
  path: ["installments"],
});

type InvoiceFormData = z.infer<typeof invoiceFormSchema>;

interface Supplier {
  id: string;
  name: string;
  email: string | null;
}

interface Product {
  id: string;
  name: string;
  sku: string;
  basePrice: number;
  unit: {
    name: string;
    abbreviation: string;
  } | null;
}

interface PurchaseOrder {
  id: string;
  orderDate: string;
  status: string;
  taxPercentage?: number;
  supplier: {
    id: string;
    name: string;
  };
  items: Array<{
    id: string;
    productId: string;
    quantity: number;
    unitPrice: number;
    product: {
      id: string;
      name: string;
      sku: string;
    };
  }>;
  invoices?: Array<{
    id: string;
    invoiceNumber: string;
    status: string;
  }>;
}

export default function NewInvoicePage() {
  const router = useRouter();
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [loading, setLoading] = useState(false);
  const [generatingNumber, setGeneratingNumber] = useState(false);
  const [installmentSectionOpen, setInstallmentSectionOpen] = useState(false);
  const [installmentValidationError, setInstallmentValidationError] = useState<string | null>(null);
  const [manuallyEditedInstallments, setManuallyEditedInstallments] = useState<Set<number>>(new Set());
  const [invoiceNumberError, setInvoiceNumberError] = useState<string | null>(null);
  const [selectedPOId, setSelectedPOId] = useState<string | null>(null);

  const form = useForm<InvoiceFormData>({
    resolver: zodResolver(invoiceFormSchema),
    defaultValues: {
      invoiceNumber: "", // Initialize as empty string
      purchaseOrderId: "", // Initialize as empty string
      supplierId: "", // Initialize as empty string
      invoiceDate: new Date(),
      dueDate: undefined, // Explicitly set as undefined
      taxPercentage: 11, // Default VAT
      notes: "", // Initialize as empty string
      enableInstallments: false,
      numberOfInstallments: 2,
      installments: [],
      items: [
        {
          productId: "",
          description: "",
          quantity: 1,
          unitPrice: 0,
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  const {
    fields: installmentFields,
    append: appendInstallment,
    remove: removeInstallment,
    replace: replaceInstallments
  } = useFieldArray({
    control: form.control,
    name: "installments",
  });

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [suppliersRes, productsRes, posRes] = await Promise.all([
          fetch("/api/suppliers?isActive=true"),
          fetch("/api/products?limit=100"),
          fetch("/api/purchase-orders?limit=100"), // Fetch more POs and filter client-side
        ]);

        if (suppliersRes.ok) {
          const suppliersData = await suppliersRes.json();
          setSuppliers(suppliersData.suppliers || []);
        }

        if (productsRes.ok) {
          const productsData = await productsRes.json();
          setProducts(productsData.products || []);
        }

        if (posRes.ok) {
          const posData = await posRes.json();
          // Filter POs to only show those eligible for invoice creation
          const eligiblePOs = (posData.purchaseOrders || []).filter((po: any) => {
            // PO must be in correct status
            if (!['ORDERED', 'PARTIALLY_RECEIVED', 'RECEIVED'].includes(po.status)) {
              return false;
            }

            // PO is eligible if it has no invoices OR only has CANCELLED/REJECTED invoices
            if (!po.invoices || po.invoices.length === 0) {
              return true;
            }

            // Check if all invoices are CANCELLED or REJECTED
            const hasActiveInvoices = po.invoices.some((invoice: any) =>
              ['PENDING', 'APPROVED'].includes(invoice.status)
            );

            return !hasActiveInvoices;
          });
          setPurchaseOrders(eligiblePOs);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
    // Auto-generate invoice number when form loads
    autoGenerateInvoiceNumber();
  }, []);

  // Handle pre-filling from URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const poId = urlParams.get('po');

    if (poId && purchaseOrders.length > 0) {
      const selectedPO = purchaseOrders.find(po => po.id === poId);
      if (selectedPO) {
        form.setValue("purchaseOrderId", poId);
        handlePOSelection(poId);
      }
    }
  }, [purchaseOrders]);

  // Generate invoice number via API
  const generateInvoiceNumberFromAPI = async (): Promise<string> => {
    const response = await fetch("/api/invoices/generate-number");
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to generate invoice number");
    }
    const data = await response.json();
    return data.invoiceNumber;
  };

  // Handle manual invoice number generation
  const handleGenerateInvoiceNumber = async () => {
    try {
      setGeneratingNumber(true);
      setInvoiceNumberError(null);
      const invoiceNumber = await generateInvoiceNumberFromAPI();
      form.setValue("invoiceNumber", invoiceNumber);
    } catch (error) {
      console.error("Error generating invoice number:", error);
      setInvoiceNumberError(error instanceof Error ? error.message : "Failed to generate invoice number");
    } finally {
      setGeneratingNumber(false);
    }
  };

  // Auto-generate invoice number when form loads
  const autoGenerateInvoiceNumber = async () => {
    try {
      const currentInvoiceNumber = form.getValues("invoiceNumber");
      // Only auto-generate if the field is empty
      if (!currentInvoiceNumber) {
        setGeneratingNumber(true);
        const invoiceNumber = await generateInvoiceNumberFromAPI();
        form.setValue("invoiceNumber", invoiceNumber);
        setGeneratingNumber(false);
      }
    } catch (error) {
      console.error("Error auto-generating invoice number:", error);
      setGeneratingNumber(false);
      // Fail silently for auto-generation, user can still generate manually
      // Don't set error state for auto-generation to avoid confusing the user
    }
  };

  // Handle PO selection
  const handlePOSelection = (poId: string) => {
    const selectedPO = purchaseOrders.find(po => po.id === poId);
    if (selectedPO) {
      setSelectedPOId(poId);
      form.setValue("supplierId", selectedPO.supplier.id);

      // Copy tax percentage from PO if available
      if (selectedPO.taxPercentage !== undefined) {
        form.setValue("taxPercentage", selectedPO.taxPercentage);
      }

      // Clear existing items and add PO items
      form.setValue("items", selectedPO.items.map(item => ({
        productId: item.productId,
        description: item.product.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
      })));
    } else if (poId === "") {
      // Handle clearing PO selection
      setSelectedPOId(null);
      // Reset tax percentage to default when clearing PO
      form.setValue("taxPercentage", 11);
    }
  };

  // Handle product selection
  const handleProductSelection = (index: number, productId: string) => {
    const selectedProduct = products.find(p => p.id === productId);
    if (selectedProduct) {
      form.setValue(`items.${index}.description`, selectedProduct.name);
      form.setValue(`items.${index}.unitPrice`, selectedProduct.basePrice);
    }
  };

  // Generate installments based on number and total
  const generateInstallments = (numberOfInstallments: number, total: number) => {
    const installmentAmount = total / numberOfInstallments;
    const installments = [];

    for (let i = 0; i < numberOfInstallments; i++) {
      const dueDate = addMonths(form.getValues("invoiceDate"), i + 1);
      installments.push({
        dueDate,
        amount: i === numberOfInstallments - 1
          ? total - (installmentAmount * (numberOfInstallments - 1)) // Last installment gets remainder
          : installmentAmount,
        description: `${i + 1}${i === 0 ? 'st' : i === 1 ? 'nd' : i === 2 ? 'rd' : 'th'} Installment`,
      });
    }

    return installments;
  };

  // Redistribute installment amounts when one is manually changed
  const redistributeInstallmentAmounts = (changedIndex: number, newAmount: number) => {
    const currentInstallments = form.getValues("installments") || [];
    const totalInvoiceAmount = totals.total;

    // Ensure the new amount is not negative
    const validatedAmount = Math.max(0, newAmount);

    // Mark this installment as manually edited
    const updatedManuallyEdited = new Set(manuallyEditedInstallments).add(changedIndex);
    setManuallyEditedInstallments(updatedManuallyEdited);

    // Update the changed installment first
    form.setValue(`installments.${changedIndex}.amount`, validatedAmount);

    // Find installments that haven't been manually edited (excluding the current one)
    const unmodifiedIndices = currentInstallments
      .map((_, index) => index)
      .filter(index => index !== changedIndex && !updatedManuallyEdited.has(index));

    if (unmodifiedIndices.length === 0) {
      // If all other installments have been manually edited, just update the current one
      return;
    }

    // Calculate total amount from ALL manually edited installments (including the current one)
    let totalManuallyEditedAmount = validatedAmount; // Start with current installment

    // Add amounts from other manually edited installments
    updatedManuallyEdited.forEach(index => {
      if (index !== changedIndex) {
        const amount = currentInstallments[index]?.amount || 0;
        totalManuallyEditedAmount += amount;
      }
    });

    // Calculate remaining amount after ALL manually edited installments
    const remainingAmount = totalInvoiceAmount - totalManuallyEditedAmount;

    // Handle edge case where manual amounts exceed total
    if (remainingAmount < 0) {
      // Set all unmodified installments to 0
      unmodifiedIndices.forEach(index => {
        form.setValue(`installments.${index}.amount`, 0);
      });
      return;
    }

    // Distribute remaining amount evenly among unmodified installments
    const amountPerUnmodified = remainingAmount / unmodifiedIndices.length;

    // Update unmodified installments with redistributed amounts
    unmodifiedIndices.forEach((index, i) => {
      // For the last unmodified installment, use remainder to avoid rounding errors
      const amount = i === unmodifiedIndices.length - 1
        ? remainingAmount - (amountPerUnmodified * (unmodifiedIndices.length - 1))
        : amountPerUnmodified;

      form.setValue(`installments.${index}.amount`, Math.max(0, amount));
    });
  };

  // Handle installment toggle
  const handleInstallmentToggle = (enabled: boolean) => {
    form.setValue("enableInstallments", enabled);
    setInstallmentSectionOpen(enabled);

    if (enabled) {
      const numberOfInstallments = form.getValues("numberOfInstallments") || 2;
      const installments = generateInstallments(numberOfInstallments, totals.total);
      replaceInstallments(installments);
      // Reset manually edited tracking when toggling installments
      setManuallyEditedInstallments(new Set());
    } else {
      replaceInstallments([]);
      form.setValue("dueDate", undefined);
      setManuallyEditedInstallments(new Set());
    }
  };

  // Handle number of installments change
  const handleInstallmentCountChange = (count: number) => {
    form.setValue("numberOfInstallments", count);
    const installments = generateInstallments(count, totals.total);
    replaceInstallments(installments);
    // Reset manually edited tracking when changing installment count
    setManuallyEditedInstallments(new Set());
  };

  // Validate installment amounts
  const validateInstallments = () => {
    const installments = form.getValues("installments") || [];
    const totalInstallmentAmount = installments.reduce((sum, inst) => sum + (inst.amount || 0), 0);
    const invoiceTotal = totals.total;

    if (Math.abs(totalInstallmentAmount - invoiceTotal) > 0.01) {
      setInstallmentValidationError(
        `Installment amounts (${formatCurrency(totalInstallmentAmount)}) must equal invoice total (${formatCurrency(invoiceTotal)})`
      );
      return false;
    }

    // Check due dates are in chronological order
    for (let i = 1; i < installments.length; i++) {
      if (installments[i].dueDate <= installments[i - 1].dueDate) {
        setInstallmentValidationError("Due dates must be in chronological order");
        return false;
      }
    }

    setInstallmentValidationError(null);
    return true;
  };

  // Calculate totals
  const watchedItems = form.watch("items");
  const watchedTaxPercentage = form.watch("taxPercentage") || 0;
  const watchedEnableInstallments = form.watch("enableInstallments");
  const watchedInstallments = form.watch("installments");

  const totals = calculateInvoiceTotals(
    watchedItems.map(item => ({
      quantity: item.quantity || 0,
      unitPrice: item.unitPrice || 0,
    })),
    watchedTaxPercentage
  );

  // Update installment amounts when total changes
  React.useEffect(() => {
    if (watchedEnableInstallments && installmentFields.length > 0) {
      const numberOfInstallments = form.getValues("numberOfInstallments") || 2;
      const newInstallments = generateInstallments(numberOfInstallments, totals.total);

      // Preserve custom due dates if they exist
      const currentInstallments = form.getValues("installments") || [];
      newInstallments.forEach((newInst, index) => {
        if (currentInstallments[index]?.dueDate) {
          newInst.dueDate = currentInstallments[index].dueDate;
        }
      });

      replaceInstallments(newInstallments);
      // Reset manually edited tracking when total changes (e.g., items added/removed)
      setManuallyEditedInstallments(new Set());
    }
  }, [totals.total, watchedEnableInstallments]);

  // Validate installments when they change
  React.useEffect(() => {
    if (watchedEnableInstallments && watchedInstallments && watchedInstallments.length > 0) {
      validateInstallments();
    }
  }, [watchedInstallments, totals.total]);

  // Submit form
  const onSubmit = async (data: InvoiceFormData) => {
    try {
      setLoading(true);

      // Validate installments if enabled
      if (data.enableInstallments && !validateInstallments()) {
        setLoading(false);
        return;
      }

      // Prepare submit data with proper formatting
      const submitData = {
        ...data,
        invoiceDate: data.invoiceDate.toISOString(),
        dueDate: data.dueDate?.toISOString(),
        // Only include purchaseOrderId if it's not empty
        purchaseOrderId: data.purchaseOrderId || undefined,
        installments: data.enableInstallments
          ? data.installments?.map(inst => ({
              ...inst,
              dueDate: inst.dueDate.toISOString(),
            }))
          : undefined,
      };

      console.log("Submitting invoice data:", submitData);

      const response = await fetch("/api/invoices", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("API Error Response:", errorData);

        // Show more specific error messages
        if (errorData.details) {
          console.error("Validation Details:", errorData.details);
        }

        throw new Error(errorData.error || "Failed to create invoice");
      }

      const invoice = await response.json();
      console.log("Invoice created successfully:", invoice);
      router.push(`/invoices/${invoice.id}`);
    } catch (error) {
      console.error("Error creating invoice:", error);

      // More detailed error logging
      if (error instanceof Error) {
        console.error("Error message:", error.message);
        console.error("Error stack:", error.stack);
      }

      // TODO: Add toast notification for user feedback
      alert(`Failed to create invoice: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="Create New Invoice"
        description="Create a new invoice manually or from a purchase order"
      />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Invoice Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    {/* Invoice Number */}
                    <FormField
                      control={form.control}
                      name="invoiceNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Invoice Number</FormLabel>
                          <div className="flex gap-2">
                            <FormControl>
                              <Input
                                placeholder="INV-2024-01-0001"
                                {...field}
                                className={invoiceNumberError ? "border-red-500" : ""}
                              />
                            </FormControl>
                            <Button
                              type="button"
                              variant="outline"
                              onClick={handleGenerateInvoiceNumber}
                              disabled={generatingNumber}
                              className="whitespace-nowrap"
                            >
                              {generatingNumber ? "Generating..." : "Generate"}
                            </Button>
                          </div>
                          {invoiceNumberError && (
                            <p className="text-sm text-red-600 mt-1">{invoiceNumberError}</p>
                          )}
                          <p className="text-xs text-muted-foreground">
                            Invoice number is auto-generated when the form loads. Click "Generate" to create a new number.
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Purchase Order (Optional) */}
                    <FormField
                      control={form.control}
                      name="purchaseOrderId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Purchase Order (Optional)</FormLabel>
                          <p className="text-xs text-muted-foreground mb-2">
                            Select a PO to auto-fill invoice details. Only POs with ORDERED, PARTIALLY_RECEIVED, or RECEIVED status that don't have PENDING or APPROVED invoices are shown.
                          </p>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              handlePOSelection(value);
                            }}
                            value={field.value || ""}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select PO to auto-fill" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {purchaseOrders.length === 0 ? (
                                <div className="p-2 text-sm text-muted-foreground">
                                  No eligible purchase orders found. POs must be in ORDERED, PARTIALLY_RECEIVED, or RECEIVED status and not have PENDING or APPROVED invoices.
                                </div>
                              ) : (
                                purchaseOrders.map((po) => (
                                  <SelectItem key={po.id} value={po.id}>
                                    <div className="flex items-center justify-between w-full">
                                      <span>{po.id.slice(-8).toUpperCase()} - {po.supplier.name}</span>
                                      <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                                        po.status === 'ORDERED' ? 'bg-purple-100 text-purple-800' :
                                        po.status === 'PARTIALLY_RECEIVED' ? 'bg-orange-100 text-orange-800' :
                                        'bg-green-100 text-green-800'
                                      }`}>
                                        {po.status.replace('_', ' ')}
                                      </span>
                                    </div>
                                  </SelectItem>
                                ))
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Supplier */}
                    <FormField
                      control={form.control}
                      name="supplierId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Supplier</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value || ""}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select supplier" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {suppliers.map((supplier) => (
                                <SelectItem key={supplier.id} value={supplier.id}>
                                  {supplier.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Tax Percentage */}
                    <FormField
                      control={form.control}
                      name="taxPercentage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tax Percentage (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="11"
                              value={field.value || 0}
                              onChange={(e) => {
                                const value = e.target.valueAsNumber;
                                field.onChange(isNaN(value) ? 0 : value);
                              }}
                              onBlur={field.onBlur}
                              name={field.name}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Invoice Date */}
                    <FormField
                      control={form.control}
                      name="invoiceDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Invoice Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Due Date - Only show if installments are disabled */}
                    {!watchedEnableInstallments && (
                      <FormField
                        control={form.control}
                        name="dueDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Due Date (Optional)</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="outline"
                                    className={cn(
                                      "w-full pl-3 text-left font-normal",
                                      !field.value && "text-muted-foreground"
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "PPP")
                                    ) : (
                                      <span>Pick a due date</span>
                                    )}
                                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) => date < new Date()}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Installment Payment Toggle */}
                    <FormField
                      control={form.control}
                      name="enableInstallments"
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="enableInstallments"
                              checked={field.value}
                              onCheckedChange={handleInstallmentToggle}
                            />
                            <FormLabel htmlFor="enableInstallments" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                              Enable Installment Payments
                            </FormLabel>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Split the invoice into multiple payments with different due dates
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Installment Configuration */}
                  {watchedEnableInstallments && (
                    <Collapsible open={installmentSectionOpen} onOpenChange={setInstallmentSectionOpen}>
                      <CollapsibleTrigger asChild>
                        <Button variant="outline" className="w-full justify-between">
                          <span>Installment Configuration</span>
                          {installmentSectionOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                        </Button>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="space-y-4 mt-4">
                        {/* Number of Installments */}
                        <FormField
                          control={form.control}
                          name="numberOfInstallments"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Number of Installments</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min={2}
                                  max={12}
                                  placeholder="2"
                                  value={field.value || 2}
                                  onChange={(e) => {
                                    const value = e.target.valueAsNumber;
                                    const safeValue = isNaN(value) ? 2 : value;
                                    field.onChange(safeValue);
                                    if (safeValue >= 2 && safeValue <= 12) {
                                      handleInstallmentCountChange(safeValue);
                                    }
                                  }}
                                  onBlur={field.onBlur}
                                  name={field.name}
                                />
                              </FormControl>
                              <p className="text-xs text-muted-foreground">
                                Choose between 2 and 12 installments
                              </p>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Installment Schedule */}
                        {installmentFields.length > 0 && (
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <h4 className="text-sm font-medium">Installment Schedule</h4>
                              <span className="text-xs text-muted-foreground">
                                Total: {formatCurrency(totals.total)}
                              </span>
                            </div>

                            {installmentValidationError && (
                              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                                {installmentValidationError}
                              </div>
                            )}

                            <div className="space-y-3">
                              {installmentFields.map((field, index) => (
                                <div key={field.id} className="grid grid-cols-3 gap-3 p-3 border rounded-lg">
                                  <FormField
                                    control={form.control}
                                    name={`installments.${index}.dueDate`}
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel className="text-xs">Due Date</FormLabel>
                                        <Popover>
                                          <PopoverTrigger asChild>
                                            <FormControl>
                                              <Button
                                                variant="outline"
                                                className={cn(
                                                  "w-full pl-3 text-left font-normal text-xs",
                                                  !field.value && "text-muted-foreground"
                                                )}
                                              >
                                                {field.value ? (
                                                  format(field.value, "MMM dd, yyyy")
                                                ) : (
                                                  <span>Pick date</span>
                                                )}
                                                <CalendarIcon className="ml-auto h-3 w-3 opacity-50" />
                                              </Button>
                                            </FormControl>
                                          </PopoverTrigger>
                                          <PopoverContent className="w-auto p-0" align="start">
                                            <Calendar
                                              mode="single"
                                              selected={field.value}
                                              onSelect={field.onChange}
                                              disabled={(date) => date < new Date()}
                                              initialFocus
                                            />
                                          </PopoverContent>
                                        </Popover>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <FormField
                                    control={form.control}
                                    name={`installments.${index}.amount`}
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel className="text-xs flex items-center gap-1">
                                          Amount
                                          {manuallyEditedInstallments.has(index) && (
                                            <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                              Manual
                                            </span>
                                          )}
                                        </FormLabel>
                                        <FormControl>
                                          <Input
                                            type="number"
                                            step="0.01"
                                            placeholder="0.00"
                                            className="text-xs"
                                            value={field.value || 0}
                                            onChange={(e) => {
                                              const value = e.target.valueAsNumber;
                                              const amount = isNaN(value) ? 0 : value;

                                              // Use redistribution logic instead of just setting the value
                                              redistributeInstallmentAmounts(index, amount);
                                            }}
                                            onBlur={field.onBlur}
                                            name={field.name}
                                          />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />

                                  <FormField
                                    control={form.control}
                                    name={`installments.${index}.description`}
                                    render={({ field }) => (
                                      <FormItem>
                                        <FormLabel className="text-xs">Description</FormLabel>
                                        <FormControl>
                                          <Input
                                            placeholder="e.g., 1st Installment"
                                            className="text-xs"
                                            {...field}
                                            value={field.value || ""}
                                          />
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    )}
                                  />
                                </div>
                              ))}
                            </div>

                            {/* Installment Summary */}
                            <div className="p-3 bg-gray-50 rounded-lg">
                              <h5 className="text-sm font-medium mb-2">Payment Schedule Summary</h5>
                              <div className="space-y-1 text-xs">
                                {installmentFields.map((_, index) => {
                                  const installment = form.watch(`installments.${index}`);
                                  const isManuallyEdited = manuallyEditedInstallments.has(index);
                                  return (
                                    <div key={index} className="flex justify-between">
                                      <span className="flex items-center gap-1">
                                        {installment?.description || `Installment ${index + 1}`}
                                        {isManuallyEdited && (
                                          <span className="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                            M
                                          </span>
                                        )}
                                      </span>
                                      <span>
                                        {installment?.dueDate ? format(installment.dueDate, "MMM dd") : "No date"} - {formatCurrency(installment?.amount || 0)}
                                      </span>
                                    </div>
                                  );
                                })}
                                <div className="border-t pt-1 mt-2 font-medium flex justify-between">
                                  <span>Total:</span>
                                  <span>{formatCurrency(installmentFields.reduce((sum, _, index) => {
                                    const amount = form.watch(`installments.${index}.amount`) || 0;
                                    return sum + amount;
                                  }, 0))}</span>
                                </div>
                              </div>

                              {/* Redistribution Info */}
                              <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
                                <p className="font-medium mb-1">💡 Smart Redistribution</p>
                                <p>When you manually edit an installment amount, the remaining balance is automatically redistributed evenly among other installments.</p>
                              </div>
                            </div>
                          </div>
                        )}
                      </CollapsibleContent>
                    </Collapsible>
                  )}

                  {/* Notes */}
                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Additional notes for this invoice..."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Invoice Items */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Invoice Items</CardTitle>
                    {selectedPOId ? (
                      <div className="text-sm text-muted-foreground">
                        Items from PO {selectedPOId.slice(-8).toUpperCase()} (Read-only)
                      </div>
                    ) : (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          append({
                            productId: "",
                            description: "",
                            quantity: 1,
                            unitPrice: 0,
                          })
                        }
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Item
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Unit Price</TableHead>
                        <TableHead>Subtotal</TableHead>
                        <TableHead></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {fields.map((field, index) => {
                        const quantity = form.watch(`items.${index}.quantity`) || 0;
                        const unitPrice = form.watch(`items.${index}.unitPrice`) || 0;
                        const subtotal = quantity * unitPrice;

                        return (
                          <TableRow key={field.id}>
                            <TableCell>
                              {selectedPOId ? (
                                // Read-only display for PO items
                                <div className="text-sm">
                                  {(() => {
                                    const productId = form.watch(`items.${index}.productId`);
                                    const product = products.find(p => p.id === productId);
                                    return product ? `${product.name} (${product.sku})` : 'Unknown Product';
                                  })()}
                                </div>
                              ) : (
                                // Editable select for manual items
                                <FormField
                                  control={form.control}
                                  name={`items.${index}.productId`}
                                  render={({ field }) => (
                                    <FormItem>
                                      <Select
                                        onValueChange={(value) => {
                                          field.onChange(value);
                                          handleProductSelection(index, value);
                                        }}
                                        value={field.value || ""}
                                      >
                                        <FormControl>
                                          <SelectTrigger>
                                            <SelectValue placeholder="Select product" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          {products.map((product) => (
                                            <SelectItem key={product.id} value={product.id}>
                                              {product.name} ({product.sku})
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}
                            </TableCell>
                            <TableCell>
                              {selectedPOId ? (
                                // Read-only display for PO items
                                <div className="text-sm">
                                  {form.watch(`items.${index}.description`) || 'No description'}
                                </div>
                              ) : (
                                // Editable input for manual items
                                <FormField
                                  control={form.control}
                                  name={`items.${index}.description`}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormControl>
                                        <Input placeholder="Item description" {...field} value={field.value || ""} />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}
                            </TableCell>
                            <TableCell>
                              {selectedPOId ? (
                                // Read-only display for PO items
                                <div className="text-sm">
                                  {form.watch(`items.${index}.quantity`) || 0}
                                </div>
                              ) : (
                                // Editable input for manual items
                                <FormField
                                  control={form.control}
                                  name={`items.${index}.quantity`}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormControl>
                                        <Input
                                          type="number"
                                          placeholder="1"
                                          value={field.value || 0}
                                          onChange={(e) => {
                                            const value = e.target.valueAsNumber;
                                            field.onChange(isNaN(value) ? 0 : value);
                                          }}
                                          onBlur={field.onBlur}
                                          name={field.name}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}
                            </TableCell>
                            <TableCell>
                              {selectedPOId ? (
                                // Read-only display for PO items
                                <div className="text-sm">
                                  {formatCurrency(form.watch(`items.${index}.unitPrice`) || 0)}
                                </div>
                              ) : (
                                // Editable input for manual items
                                <FormField
                                  control={form.control}
                                  name={`items.${index}.unitPrice`}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormControl>
                                        <Input
                                          type="number"
                                          placeholder="0"
                                          value={field.value || 0}
                                          onChange={(e) => {
                                            const value = e.target.valueAsNumber;
                                            field.onChange(isNaN(value) ? 0 : value);
                                          }}
                                          onBlur={field.onBlur}
                                          name={field.name}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}
                            </TableCell>
                            <TableCell className="font-medium">
                              {formatCurrency(subtotal)}
                            </TableCell>
                            <TableCell>
                              {!selectedPOId && fields.length > 1 && (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => remove(index)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>

            {/* Summary Sidebar */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Invoice Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>{formatCurrency(totals.subtotal)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax ({watchedTaxPercentage}%):</span>
                      <span>{formatCurrency(totals.tax)}</span>
                    </div>
                    <div className="border-t pt-2">
                      <div className="flex justify-between font-semibold text-lg">
                        <span>Total:</span>
                        <span>{formatCurrency(totals.total)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Installment Summary in Sidebar */}
                  {watchedEnableInstallments && installmentFields.length > 0 && (
                    <div className="border-t pt-4">
                      <h4 className="text-sm font-medium mb-3">Payment Schedule</h4>
                      <div className="space-y-2">
                        {installmentFields.map((_, index) => {
                          const installment = form.watch(`installments.${index}`);
                          return (
                            <div key={index} className="flex justify-between text-sm">
                              <span className="text-muted-foreground">
                                {installment?.description || `Payment ${index + 1}`}
                              </span>
                              <div className="text-right">
                                <div>{formatCurrency(installment?.amount || 0)}</div>
                                <div className="text-xs text-muted-foreground">
                                  {installment?.dueDate ? format(installment.dueDate, "MMM dd, yyyy") : "No date"}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      {installmentValidationError && (
                        <div className="mt-2 p-2 text-xs text-red-600 bg-red-50 border border-red-200 rounded">
                          {installmentValidationError}
                        </div>
                      )}
                    </div>
                  )}

                  <div className="space-y-2">
                    <Button
                      type="submit"
                      className="w-full"
                      disabled={loading || (watchedEnableInstallments && !!installmentValidationError)}
                    >
                      {loading ? "Creating..." : "Create Invoice"}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      className="w-full"
                      onClick={() => router.push("/invoices")}
                    >
                      Cancel
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </form>
      </Form>
    </MainLayout>
  );
}
